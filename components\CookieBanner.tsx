'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { 
  hasUserMadeChoice, 
  acceptAllCookies, 
  rejectAllCookies,
  getSavedPreferences 
} from '@/lib/consent';

interface CookieBannerProps {
  onOpenSettings: () => void;
}

export function CookieBanner({ onOpenSettings }: CookieBannerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Aguardar um pouco para evitar flash
    const timer = setTimeout(() => {
      setIsLoaded(true);
      // Mostrar banner apenas se o usuário não fez escolha
      if (!hasUserMadeChoice()) {
        setIsVisible(true);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleAcceptAll = () => {
    acceptAllCookies();
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    rejectAllCookies();
    setIsVisible(false);
  };

  const handleCustomize = () => {
    onOpenSettings();
    setIsVisible(false);
  };

  const handleClose = () => {
    // Se fechar sem escolher, assumir rejeição
    if (!hasUserMadeChoice()) {
      rejectAllCookies();
    }
    setIsVisible(false);
  };

  if (!isLoaded || !isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
      <div className="max-w-7xl mx-auto p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          {/* Ícone e texto */}
          <div className="flex items-start gap-3 flex-1">
            <Cookie className="w-6 h-6 text-purple-600 mt-1 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-2">
                Utilizamos cookies para melhorar sua experiência
              </h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                Este site utiliza cookies essenciais para funcionamento, cookies de análise para melhorar 
                a experiência do usuário e cookies de marketing para personalização de conteúdo. 
                Você pode gerenciar suas preferências a qualquer momento.{' '}
                <a 
                  href="/privacidade" 
                  className="text-purple-600 hover:text-purple-700 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Saiba mais
                </a>
              </p>
            </div>
          </div>

          {/* Botões */}
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <button
              onClick={handleCustomize}
              className="flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Settings className="w-4 h-4" />
              Personalizar
            </button>
            
            <button
              onClick={handleRejectAll}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Rejeitar
            </button>
            
            <button
              onClick={handleAcceptAll}
              className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              Aceitar Todos
            </button>
          </div>

          {/* Botão fechar */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 sm:relative sm:top-0 sm:right-0 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Fechar banner de cookies"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}


