import type { Metadata, Viewport } from "next";
import { Outfit } from "next/font/google";
import "./globals.css";
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { GoogleAdSense } from '@/components/GoogleAdSense';
import { ConsentManager } from '@/components/ConsentManager';

const outfit = Outfit({
  variable: "--font-outfit",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Cereja - Calculadora 4:6",
  description: "Cereja - Calculadora para o método 4:6 de Tetsu Kasuya",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Cereja",
  },
  formatDetection: {
    telephone: false,
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: "#8b5cf6",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${outfit.variable} antialiased`}
      >
        <GoogleAnalytics />
        <GoogleAdSense />
        {children}
        <ConsentManager />
      </body>
    </html>
  );
}
