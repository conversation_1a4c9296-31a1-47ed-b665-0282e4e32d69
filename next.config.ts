import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    // Habilita o React Compiler (React 19+) de forma conservadora
    reactCompiler: true,
  },
  devIndicators: false,
  images: {
    // Otimizações de imagem para reduzir warnings de preload
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
};


export default nextConfig;
