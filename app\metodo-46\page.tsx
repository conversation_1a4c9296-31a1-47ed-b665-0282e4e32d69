'use client';

import { useState } from 'react';
import { ArrowLeft, Coffee, Clock, Droplets, Target, Award, Lightbulb, Menu, X, Home as HomeIcon, BookOpen, Info, Shield } from 'lucide-react';
import Link from 'next/link';

export default function Metodo46Page() {
  const [sidebarAberta, setSidebarAberta] = useState(false);

  const abrirSidebar = () => setSidebarAberta(true);
  const fecharSidebar = () => setSidebarAberta(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header com botão hamburger */}
      <header className="fixed sm:absolute top-4 left-4 sm:top-6 sm:left-6 z-30">
        <button
          onClick={abrirSidebar}
          className="flex items-center gap-2 p-2 text-muted-foreground hover:text-primary hover:bg-accent/50 rounded-lg sm:rounded-lg rounded-full sm:bg-transparent bg-card/90 backdrop-blur-sm shadow-lg sm:shadow-none border sm:border-0 border-border/20 transition-all"
        >
          <Menu size={20} />
          <span className="text-sm font-medium hidden sm:inline">Menu</span>
        </button>
      </header>

      {/* Sidebar */}
      {sidebarAberta && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={fecharSidebar} />
          <div className="fixed left-0 top-0 h-full w-80 bg-background border-r border-border shadow-xl">
            {/* Header da sidebar */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Coffee className="w-5 h-5 text-primary-foreground" />
                </div>
                <div>
                  <h2 className="font-semibold text-foreground">cereja</h2>
                  <p className="text-xs text-muted-foreground">método 4:6</p>
                </div>
              </div>
              <button
                onClick={fecharSidebar}
                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Navigation menu */}
            <nav className="p-6">
              <div className="space-y-2">
                <Link
                  href="/"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                >
                  <HomeIcon size={20} />
                  <span className="font-medium">Calculadora 4:6</span>
                </Link>

                <Link
                  href="/metodo-46"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-primary bg-primary/10 rounded-lg transition-colors"
                >
                  <Info size={20} />
                  <span className="font-medium">Método 4:6</span>
                </Link>

                <Link
                  href="/glossario"
                  onClick={fecharSidebar}
                  className="flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                >
                  <BookOpen size={20} />
                  <span className="font-medium">Glossário</span>
                </Link>
              </div>

              {/* Botão de doação */}
              <div className="mt-6 pt-6 border-t border-border">
                <a
                  href="https://ko-fi.com/pedrogott"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={fecharSidebar}
                  className="w-full flex items-center gap-3 px-4 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-400/30 transform hover:scale-[1.02]"
                >
                  <Coffee size={20} />
                  <div className="flex flex-col text-left">
                    <span className="font-medium text-sm">Gostou do projeto?</span>
                    <span className="text-xs opacity-80">Pague-me um café!</span>
                  </div>
                </a>
              </div>

              {/* Link de Política de Privacidade - discreto */}
              <div className="mt-4 pt-4 border-t border-border/50">
                <Link
                  href="/privacidade"
                  onClick={fecharSidebar}
                  className="block px-4 py-2 text-xs text-muted-foreground hover:text-muted-foreground/80 transition-colors text-center"
                >
                  Política de Privacidade
                </Link>
              </div>
            </nav>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/"
            className="inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Voltar ao início
          </Link>
          
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Método 4:6
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Descubra o revolucionário método de preparo de café desenvolvido por 
              Tetsu Kasuya, campeão mundial de barista
            </p>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="space-y-8">
          
          {/* Introdução sobre Tetsu Kasuya */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Award className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                Tetsu Kasuya: O Mestre por Trás do Método
              </h2>
            </div>
            <div className="prose max-w-none text-gray-600">
              <p className="mb-4">
                Tetsu Kasuya é um barista japonês que revolucionou o mundo do café ao vencer 
                o <strong>World Brewers Cup Championship em 2016</strong> com seu inovador método 4:6. 
                Sua abordagem científica e sistemática transformou a forma como entendemos 
                a extração do café no método V60.
              </p>
              <p>
                O método nasceu da busca por <strong>consistência e controle total</strong> sobre 
                o sabor do café, permitindo que qualquer pessoa possa reproduzir resultados 
                excepcionais seguindo uma fórmula precisa.
              </p>
            </div>
          </section>

          {/* Conceito 4:6 */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Target className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                O Conceito 4:6
              </h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center gap-2">
                  <span className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</span>
                  Primeiros 40%
                </h3>
                <p className="text-purple-800 text-sm">
                  <strong>Controla o sabor:</strong> As duas primeiras despejadas determinam 
                  se o café será mais ácido ou mais doce. Maior volume na primeira = mais acidez.
                </p>
              </div>
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
                  <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">6</span>
                  Últimos 60%
                </h3>
                <p className="text-blue-800 text-sm">
                  <strong>Controla a força:</strong> As três últimas despejadas determinam 
                  a intensidade do corpo. Mais despejadas = corpo mais leve.
                </p>
              </div>
            </div>
          </section>

          {/* Como Funciona */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Clock className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                Como Funciona o Método
              </h2>
            </div>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">📏 Proporção Base</h3>
                <p className="text-gray-600 text-sm">
                  Sempre use a proporção <strong>1:15</strong> (1g de café para 15ml de água). 
                  Exemplo: 20g de café = 300ml de água total.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">⏱️ Timing Preciso</h3>
                <p className="text-gray-600 text-sm mb-2">
                  Cada despejada acontece em intervalos de <strong>45 segundos</strong>:
                </p>
                <ul className="text-gray-600 text-sm space-y-1 ml-4">
                  <li>• 0:00 - Primeira despejada</li>
                  <li>• 0:45 - Segunda despejada</li>
                  <li>• 1:30 - Terceira despejada</li>
                  <li>• 2:15 - Quarta despejada</li>
                  <li>• 3:00 - Quinta despejada</li>
                </ul>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">💧 Divisão da Água</h3>
                <p className="text-gray-600 text-sm">
                  A água total é dividida igualmente entre as 5 despejadas. 
                  Para 300ml total = 60ml por despejada.
                </p>
              </div>
            </div>
          </section>

          {/* Perfis de Sabor */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Coffee className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                Controlando o Sabor
              </h2>
            </div>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl mb-2">🍋</div>
                <h3 className="font-semibold text-orange-900 mb-2">Mais Acidez</h3>
                <p className="text-orange-800 text-sm">
                  <strong>50% + 50%</strong> nos primeiros 40%<br/>
                  Primeira despejada maior
                </p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl mb-2">⚖️</div>
                <h3 className="font-semibold text-green-900 mb-2">Equilibrado</h3>
                <p className="text-green-800 text-sm">
                  <strong>50% + 50%</strong> nos primeiros 40%<br/>
                  Despejadas iguais
                </p>
              </div>
              <div className="text-center p-4 bg-amber-50 rounded-lg">
                <div className="text-2xl mb-2">🍯</div>
                <h3 className="font-semibold text-amber-900 mb-2">Mais Doçura</h3>
                <p className="text-amber-800 text-sm">
                  <strong>30% + 70%</strong> nos primeiros 40%<br/>
                  Segunda despejada maior
                </p>
              </div>
            </div>
          </section>

          {/* Benefícios */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Droplets className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                Por Que o Método 4:6 Funciona?
              </h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">✅ Vantagens</h3>
                <ul className="space-y-2 text-gray-600 text-sm">
                  <li>• <strong>Reprodutibilidade:</strong> Resultados consistentes</li>
                  <li>• <strong>Controle total:</strong> Ajuste sabor e corpo independentemente</li>
                  <li>• <strong>Simplicidade:</strong> Fórmula fácil de seguir</li>
                  <li>• <strong>Versatilidade:</strong> Funciona com diferentes cafés</li>
                  <li>• <strong>Eficiência:</strong> Extração otimizada em 5 minutos</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">🔄 Diferenças</h3>
                <ul className="space-y-2 text-gray-600 text-sm">
                  <li>• <strong>vs. Pour Over tradicional:</strong> Timing estruturado</li>
                  <li>• <strong>vs. Bloom único:</strong> Múltiplas extrações controladas</li>
                  <li>• <strong>vs. Despejo contínuo:</strong> Fases distintas de sabor</li>
                  <li>• <strong>vs. Métodos intuitivos:</strong> Abordagem científica</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Dicas Práticas */}
          <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Lightbulb className="w-6 h-6 text-purple-600" />
              <h2 className="text-2xl font-semibold text-gray-900">
                Dicas para o Sucesso
              </h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="bg-blue-50 rounded-lg p-3">
                  <h4 className="font-semibold text-blue-900 text-sm mb-1">🌡️ Temperatura</h4>
                  <p className="text-blue-800 text-xs">92-96°C para cafés claros, 88-92°C para escuros</p>
                </div>
                <div className="bg-green-50 rounded-lg p-3">
                  <h4 className="font-semibold text-green-900 text-sm mb-1">⚡ Moagem</h4>
                  <p className="text-green-800 text-xs">Média-grossa, similar a sal grosso</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-3">
                  <h4 className="font-semibold text-purple-900 text-sm mb-1">📐 Filtro</h4>
                  <p className="text-purple-800 text-xs">V60 com filtro original, enxaguado previamente</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="bg-orange-50 rounded-lg p-3">
                  <h4 className="font-semibold text-orange-900 text-sm mb-1">💧 Despejo</h4>
                  <p className="text-orange-800 text-xs">Centro para fora, movimento circular suave</p>
                </div>
                <div className="bg-red-50 rounded-lg p-3">
                  <h4 className="font-semibold text-red-900 text-sm mb-1">⏰ Cronômetro</h4>
                  <p className="text-red-800 text-xs">Use nossa calculadora para timing perfeito!</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">📝 Anotações</h4>
                  <p className="text-gray-700 text-xs">Registre ajustes para reproduzir o café ideal</p>
                </div>
              </div>
            </div>
          </section>

          {/* Call to Action */}
          <section className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 text-center text-white">
            <h2 className="text-2xl font-bold mb-3">
              Pronto para Experimentar?
            </h2>
            <p className="mb-4 opacity-90">
              Use nossa calculadora para aplicar o método 4:6 com precisão
            </p>
            <Link 
              href="/"
              className="inline-flex items-center gap-2 bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <Coffee className="w-5 h-5" />
              Ir para Calculadora
            </Link>
          </section>

        </div>
      </div>
    </div>
  );
}
