// Tipos de consentimento do Google Consent Mode v2
export type ConsentType = 'analytics_storage' | 'ad_storage' | 'ad_user_data' | 'ad_personalization';

// Estado de consentimento
export type ConsentState = 'granted' | 'denied';

// Configuração de consentimento
export interface ConsentConfig {
  analytics_storage: ConsentState;
  ad_storage: ConsentState;
  ad_user_data: ConsentState;
  ad_personalization: ConsentState;
}

// Preferências do usuário
export interface UserPreferences {
  essential: boolean; // Sempre true, não pode ser desabilitado
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
  version: string;
}

// Configuração padrão (denied para compliance com LGPD/GDPR)
export const DEFAULT_CONSENT: ConsentConfig = {
  analytics_storage: 'denied',
  ad_storage: 'denied',
  ad_user_data: 'denied',
  ad_personalization: 'denied',
};

// Preferências padrão
export const DEFAULT_PREFERENCES: UserPreferences = {
  essential: true,
  analytics: false,
  marketing: false,
  timestamp: Date.now(),
  version: '1.0',
};

// Chave para localStorage
const CONSENT_KEY = 'cereja_consent_preferences';
const CONSENT_VERSION = '1.0';

// Declaração global para gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Inicializar Google Consent Mode
export const initializeConsentMode = (): void => {
  if (typeof window === 'undefined') return;

  // Inicializar dataLayer se não existir
  window.dataLayer = window.dataLayer || [];
  
  // Função gtag
  window.gtag = function() {
    window.dataLayer.push(arguments);
  };

  // Configurar valores padrão do consent mode
  window.gtag('consent', 'default', {
    ...DEFAULT_CONSENT,
    wait_for_update: 500, // Aguardar 500ms por atualizações
  });

  // Carregar preferências salvas e aplicar
  const savedPreferences = getSavedPreferences();
  if (savedPreferences) {
    updateConsentMode(savedPreferences);
  }
};

// Salvar preferências no localStorage
export const savePreferences = (preferences: UserPreferences): void => {
  if (typeof window === 'undefined') return;

  const preferencesWithTimestamp = {
    ...preferences,
    timestamp: Date.now(),
    version: CONSENT_VERSION,
  };

  try {
    localStorage.setItem(CONSENT_KEY, JSON.stringify(preferencesWithTimestamp));
  } catch (error) {
    console.warn('Erro ao salvar preferências de cookies:', error);
  }
};

// Carregar preferências do localStorage
export const getSavedPreferences = (): UserPreferences | null => {
  if (typeof window === 'undefined') return null;

  try {
    const saved = localStorage.getItem(CONSENT_KEY);
    if (!saved) return null;

    const preferences = JSON.parse(saved) as UserPreferences;
    
    // Verificar se a versão é compatível
    if (preferences.version !== CONSENT_VERSION) {
      return null;
    }

    return preferences;
  } catch (error) {
    console.warn('Erro ao carregar preferências de cookies:', error);
    return null;
  }
};

// Verificar se o usuário já fez escolhas
export const hasUserMadeChoice = (): boolean => {
  return getSavedPreferences() !== null;
};

// Converter preferências do usuário para configuração de consentimento
export const preferencesToConsent = (preferences: UserPreferences): ConsentConfig => {
  return {
    analytics_storage: preferences.analytics ? 'granted' : 'denied',
    ad_storage: preferences.marketing ? 'granted' : 'denied',
    ad_user_data: preferences.marketing ? 'granted' : 'denied',
    ad_personalization: preferences.marketing ? 'granted' : 'denied',
  };
};

// Atualizar Google Consent Mode
export const updateConsentMode = (preferences: UserPreferences): void => {
  if (typeof window === 'undefined' || !window.gtag) return;

  const consentConfig = preferencesToConsent(preferences);
  
  window.gtag('consent', 'update', consentConfig);
  
  // Salvar preferências
  savePreferences(preferences);
};

// Aceitar todos os cookies
export const acceptAllCookies = (): void => {
  const preferences: UserPreferences = {
    essential: true,
    analytics: true,
    marketing: true,
    timestamp: Date.now(),
    version: CONSENT_VERSION,
  };

  updateConsentMode(preferences);
};

// Rejeitar cookies não essenciais
export const rejectAllCookies = (): void => {
  const preferences: UserPreferences = {
    essential: true,
    analytics: false,
    marketing: false,
    timestamp: Date.now(),
    version: CONSENT_VERSION,
  };

  updateConsentMode(preferences);
};

// Limpar preferências (para testes)
export const clearPreferences = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(CONSENT_KEY);
  } catch (error) {
    console.warn('Erro ao limpar preferências de cookies:', error);
  }
};

// Verificar se uma categoria específica foi aceita
export const isCategoryAccepted = (category: 'analytics' | 'marketing'): boolean => {
  const preferences = getSavedPreferences();
  if (!preferences) return false;
  
  return preferences[category];
};

// Obter status de consentimento atual
export const getCurrentConsentStatus = (): ConsentConfig => {
  const preferences = getSavedPreferences();
  if (!preferences) return DEFAULT_CONSENT;
  
  return preferencesToConsent(preferences);
};
